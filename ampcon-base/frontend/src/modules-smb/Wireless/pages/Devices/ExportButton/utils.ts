import { axiosGw, axiosProv } from '@/modules-smb/utils/axiosInstances';
import { DeviceWithStatus } from '@/modules-smb/hooks/Network/Devices';
import { InventoryTagApiResponse } from '@/modules-smb/models/Inventory';
import { bytesString, getRevision } from '@/modules-smb/helpers/stringHelper';
import { compactDate, formatDaysAgo } from '@/modules-smb/utils/dateFormatting';

export type ExportedDeviceInfo = {
  connected: string;
  serialNumber: string;
  name: string;
  sanity: string;
  memory: string;
  load: string;
  temperature: string;
  revision: string;
  deviceType: string;
  ip: string;
  provisioning: string;
  radiusSessions: string;
  hasGPS: string;
  uptime: string;
  lastContact: string;
  lastRecordedContact: string;
  lastUpgrade: string;
  rx: string;
  tx: string;
  twoG: string;
  fiveG: string;
  sixG: string;
  connectReason: string;
  certificateExpiry: string;
};

// export type ExportedDeviceInfo = {
//   serialNumber: string;
//   connected: 'true' | 'false';
//   firmware: string;
//   memory: number;
//   load: number;
//   temperature: number;
//   sanity: number;
//   revision: string;
//   ip: string;
//   /** Venue, Entity or subscriber name */
//   provisioning: string;
//   radiusSessions: number;
//   /** Uptime in seconds */
//   uptime: number;
//   /** Last Contact as date */
//   lastContact: string;
//   /** Last Upgrade as date */
//   lastUpgrade: string;
//   /** Rx MBs  */
//   rx: number;
//   /** Tx MBs  */
//   tx: number;
//   twoG: number;
//   fiveG: number;
//   sixG: number;
//   /** Expiry as date */
//   certificateExpiry: string;
// };

const getDevicesProvisioningStatus = async (serialNumbers: string[], venueId?: string) =>
  serialNumbers.length === 0
    ? []
    : axiosProv
      .get(`inventory?withExtendedInfo=true&select=${serialNumbers}${venueId ? `&venue=${venueId}` : ''}`)
      .then(({ data }: { data: { taglist: InventoryTagApiResponse[] } }) => {
        return serialNumbers.map((serialNumber) => {
          const found = data?.taglist?.find((tag) => tag?.serialNumber === serialNumber);

          let provisioning = 'Not Found';

          if (found) {
            if (found?.entity?.length > 0) provisioning = found?.extendedInfo?.entity?.name ?? found?.entity;
            else if (found?.venue?.length > 0) provisioning = found?.extendedInfo?.venue?.name ?? found?.venue;
            else if (found?.subscriber?.length > 0)
              provisioning = found?.extendedInfo?.subscriber?.name ?? found?.subscriber;
          }

          return {
            serialNumber,
            provisioning,
          };
        });
      })
      .catch(() => []);

const getDeviceGatewayInfo = <T,>(limit: number, offset: number, venueId?: string) => {
  const params: Record<string, any> = {
    deviceWithStatus: true,
    limit,
    offset
  };
  
  if (venueId) {
    params.venue = venueId;
  }
  
  return axiosGw.get<Record<string, unknown>>(`devices`, { params })
    .then((response: any): { data: T } => {
      const data = response?.data as { data: T };
      return data;
    }) as Promise<{ data: T }>;
}

const getAllGatewayDeviceInfo = async (
  count: number,
  initialProgress: number,
  setProgress: (progress: number) => void,
  venueId?: string,
) => {
  const progressStep = (90 - initialProgress) / Math.ceil(count / 100);
  let newProgress = initialProgress;
  let offset = 0;
  let devices: DeviceWithStatus[] = [];
  let devicesResponse: { data: { devicesWithStatus: DeviceWithStatus[] } };
  do {
    // eslint-disable-next-line no-await-in-loop
    devicesResponse = await getDeviceGatewayInfo<{ devicesWithStatus: DeviceWithStatus[] }>(100, offset, venueId);
    devices = devices?.concat(devicesResponse?.data?.devicesWithStatus ?? []);
    setProgress((newProgress += progressStep));
    offset += 100;
  } while (devicesResponse?.data?.devicesWithStatus?.length === 100);

  return devices;
};

// Helper functions for data formatting (similar to DevicesList.tsx)
const formatFourDigitNumber = (v?: number) => {
  if (v === undefined || typeof v !== 'number') return '-';
  if (v === 0) {
    return '0.00';
  }
  const str = v.toString();
  const fourthChar = str.charAt(3);
  if (fourthChar === '.') return `${str.slice(0, 3)}`;
  return `${str.slice(0, 4)}`;
};

const formatTemperature = (device: DeviceWithStatus): string => {
  if (!device?.connected || device?.temperature === 0) return '-';

  const temperature = device?.temperature > 1000 ? device?.temperature / 1000 : device?.temperature;
  return `${formatFourDigitNumber(temperature)}°C`;
};

const formatLoad = (device: DeviceWithStatus): string => {
  if (!device?.connected) return '-';
  return `${formatFourDigitNumber(device?.load)}%`;
};

const formatMemory = (device: DeviceWithStatus): string => {
  if (!device?.connected) return '-';
  return `${formatFourDigitNumber(device?.memoryUsed)}%`;
};

const formatSanity = (device: DeviceWithStatus): string => {
  if (!device?.connected) return '-';
  return `${device?.sanity}%`;
};

const formatNumberCell = (v?: number): string => {
  if (v === undefined || v === null) return '-';
  return v.toString();
};

const formatStatus = (device: DeviceWithStatus): string => {
  if (device?.blackListed) return 'Denied';
  return device?.connected ? 'Connected' : 'Disconnected';
};

const formatDate = (dateValue: number | string | undefined): string => {
  if (dateValue === undefined || dateValue === 0) return '-';
  if (typeof dateValue === 'string') return dateValue;
  try {
    return formatDaysAgo(dateValue);
  } catch (e) {
    return '-';
  }
};

const formatUptime = (device: DeviceWithStatus): string => {
  if (!device?.connected || device?.started === 0) return '0';
  return formatDaysAgo(Date.now() / 1000 - device?.started);
};

const formatData = (bytes: number): string => {
  if (bytes === undefined || bytes === null || bytes === 0) return '-';
  return bytesString(bytes);
};

const formatRadiusSessions = (hasRADIUSSessions: number | boolean): string => {
  if (typeof hasRADIUSSessions === 'number') {
    return hasRADIUSSessions.toString();
  }
  return hasRADIUSSessions ? 'true' : 'false';
};

export const getAllExportedDevicesInfo = async (setProgress: (progress: number) => void, venueId?: string) => {
  // Base Setup
  setProgress(0);
  const params: Record<string, any> = {
    countOnly: true
  };
  
  if (venueId) {
    params.venue = venueId;
  }
  
  const devicesCount = await axiosGw.get(`devices`, { params }).then((response: any): number => {
    const data = response?.data as { count: number };
    return data?.count ?? 0;
  });
  setProgress(10);

  if (devicesCount === 0) {
    setProgress(100);
    return [];
  }

  // Get Devices Info
  const devices = await getAllGatewayDeviceInfo(devicesCount, 10, setProgress, venueId);

  // 修复：确保所有设备都被考虑，而不仅仅是已分配的设备
  const serialNumbers = devices?.map((device) => device?.serialNumber) ?? [];
  const provisioningStatus = await getDevicesProvisioningStatus(serialNumbers, venueId);

  setProgress(95);

  const exportedDevicesInfo: ExportedDeviceInfo[] = devices?.map((device) => {
    const provisioning = provisioningStatus?.find((status: { serialNumber: string; provisioning: string }) => status?.serialNumber === device?.serialNumber)?.provisioning;
    return {
      connected: formatStatus(device),
      serialNumber: device?.serialNumber,
      name: device?.name ?? '-',
      sanity: formatSanity(device),
      memory: formatMemory(device),
      load: formatLoad(device),
      temperature: formatTemperature(device),
      revision: getRevision(device?.firmware) ?? '-',
      deviceType: device?.deviceType ?? '-',
      ip: device?.ipAddress ?? '-',
      provisioning: provisioning ?? '-',
      radiusSessions: formatNumberCell(typeof device.hasRADIUSSessions === 'number' ? device.hasRADIUSSessions : 0),
      hasGPS: device?.hasGPS ? 'true' : 'false',
      uptime: formatUptime(device),
      lastContact: formatDate(device?.lastContact),
      lastRecordedContact: formatDate(device?.lastRecordedContact),
      lastUpgrade: formatDate(device?.lastFWUpdate),
      rx: formatData(device?.rxBytes),
      tx: formatData(device?.txBytes),
      twoG: formatNumberCell(device?.associations_2G),
      fiveG: formatNumberCell(device?.associations_5G),
      sixG: formatNumberCell(device?.associations_6G),
      connectReason: device?.connectReason || '-',
      certificateExpiry: device?.certificateExpiryDate ? formatDate(device?.certificateExpiryDate) : '-',

    };

    // return {
    //   serialNumber: device.serialNumber,
    //   connected: device.connected ? 'true' : 'false',
    //   firmware: device.firmware,
    //   memory: device.memoryUsed,
    //   load: device.load,
    //   temperature: device.temperature,
    //   sanity: device.sanity,
    //   revision: device.compatible,
    //   ip: device.ipAddress.length > 0 ? device.ipAddress : '',
    //   provisioning: provisioning ?? '',
    //   radiusSessions: typeof device.hasRADIUSSessions === 'number' ? device.hasRADIUSSessions : 0,
    //   uptime: !device.connected || device.started === 0 ? 0 : Math.floor(Date.now() / 1000 - device.started),
    //   lastContact: typeof device.lastContact === 'string' ? '' : unixToStr(device.lastContact),
    //   lastUpgrade: typeof device.lastFWUpdate === 'string' ? '' : unixToStr(device.lastFWUpdate),
    //   rx: device.rxBytes / 1024 / 1024,
    //   tx: device.txBytes / 1024 / 1024,
    //   twoG: device.associations_2G,
    //   fiveG: device.associations_5G,
    //   sixG: device.associations_6G,
    //   certificateExpiry: device.certificateExpiryDate ? unixToStr(device.certificateExpiryDate) : '',
    // };
  });

  setProgress(100);
  return exportedDevicesInfo;
};

const getSelectDevicesGatewayInfo = <T,>(serialNumbers: string[], venueId?: string) => {
  const params: Record<string, any> = {
    deviceWithStatus: true,
    select: serialNumbers.join(',')
  };
  
  if (venueId) {
    params.venue = venueId;
  }
  
  return axiosGw.get<Record<string, unknown>>(`devices`, { params })
    .then((response: any): { data: T } => {
      const data = response?.data as { data: T };
      return data;
    }) as Promise<{ data: T }>;
};

export const getSelectExportedDevicesInfo = async (
  serialNumbers: string[],
  setProgress: (progress: number) => void,
  venueId?: string,
) => {
  // Base Setup
  setProgress(0);
  const devicesCount = serialNumbers?.length ?? 0;
  setProgress(10);

  if (devicesCount === 0) {
    setProgress(100);
    return [];
  }

  // Get Devices Info
  const devices = (await getSelectDevicesGatewayInfo<{ devicesWithStatus: DeviceWithStatus[] }>(serialNumbers, venueId))?.data?.devicesWithStatus ?? [];
  setProgress(90);

  // 修复：确保所有选定设备都被考虑
  const provSerialNumbers = devices?.map((device) => device?.serialNumber) ?? [];
  const provisioningStatus = await getDevicesProvisioningStatus(provSerialNumbers, venueId);

  setProgress(95);

  const exportedDevicesInfo: ExportedDeviceInfo[] = devices?.map((device) => {
    const provisioning = provisioningStatus?.find((status: { serialNumber: string; provisioning: string }) => status?.serialNumber === device?.serialNumber)?.provisioning;
    return {
      connected: formatStatus(device),
      serialNumber: device?.serialNumber,
      name: device?.name ?? '-',
      sanity: formatSanity(device),
      memory: formatMemory(device),
      load: formatLoad(device),
      temperature: formatTemperature(device),
      revision: getRevision(device?.firmware) ?? '-',
      deviceType: device?.deviceType ?? '-',
      ip: device?.ipAddress ?? '-',
      provisioning: provisioning ?? '-',
      radiusSessions: formatNumberCell(typeof device.hasRADIUSSessions === 'number' ? device.hasRADIUSSessions : 0),
      hasGPS: device?.hasGPS ? 'true' : 'false',
      uptime: formatUptime(device),
      lastContact: formatDate(device?.lastContact),
      lastRecordedContact: formatDate(device?.lastRecordedContact),
      lastUpgrade: formatDate(device?.lastFWUpdate),
      rx: formatData(device?.rxBytes),
      tx: formatData(device?.txBytes),
      twoG: formatNumberCell(device?.associations_2G),
      fiveG: formatNumberCell(device?.associations_5G),
      sixG: formatNumberCell(device?.associations_6G),
      connectReason: device?.connectReason || '-',
      certificateExpiry: device?.certificateExpiryDate ? formatDate(device?.certificateExpiryDate) : '-',
    };
    // return {
    //   serialNumber: device.serialNumber,
    //   connected: device.connected ? 'true' : 'false',
    //   firmware: device.firmware,
    //   memory: device.memoryUsed,
    //   load: device.load,
    //   temperature: device.temperature,
    //   sanity: device.sanity,
    //   revision: device.compatible,
    //   ip: device.ipAddress.length > 0 ? device.ipAddress : '',
    //   provisioning: provisioning ?? '',
    //   radiusSessions: typeof device.hasRADIUSSessions === 'number' ? device.hasRADIUSSessions : 0,
    //   uptime: !device.connected || device.started === 0 ? 0 : Math.floor(Date.now() / 1000 - device.started),
    //   lastContact: typeof device.lastContact === 'string' ? '' : unixToStr(device.lastContact),
    //   lastUpgrade: typeof device.lastFWUpdate === 'string' ? '' : unixToStr(device.lastFWUpdate),
    //   rx: device.rxBytes / 1024,
    //   tx: device.txBytes / 1024,
    //   twoG: device.associations_2G,
    //   fiveG: device.associations_5G,
    //   sixG: device.associations_6G,
    //   certificateExpiry: device.certificateExpiryDate ? unixToStr(device.certificateExpiryDate) : '',
    // };
  });

  setProgress(100);
  return exportedDevicesInfo;
};